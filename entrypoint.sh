#!/bin/sh
set -e

_terminate() {
  echo "Signal (SIGINT/SIGTERM) received. Waiting for gracefully shutdown..."
  kill $(jobs -p)
  wait
  exit 0
}

trap _terminate SIGINT SIGTERM

# Run migration with fallback strategies
export DATABASE_URL="postgresql://$POSTGRES_USER:$POSTGRES_PASS@$POSTGRES_HOST:$POSTGRES_PORT/${POSTGRES_DB:-agent}?sslmode=disable"
echo "DATABASE_URL: $DATABASE_URL"

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
for i in $(seq 1 30); do
  if atlas migrate status --url "$DATABASE_URL" --dir file://migrations >/dev/null 2>&1; then
    echo "✅ Database is ready"
    break
  fi
  echo "⏳ Attempt $i/30: Database not ready, waiting 2 seconds..."
  sleep 2
  if [ $i -eq 30 ]; then
    echo "❌ Database connection failed after 30 attempts"
    exit 1
  fi
done

# Run Atlas migrations with multiple fallback strategies
echo "🚀 Running Atlas migrations..."

# Strategy 1: Normal migration
if atlas migrate apply --dir "file://migrations" --url "$DATABASE_URL"; then
  echo "✅ Atlas migrations completed successfully (normal)"
else
  echo "⚠️  Normal migration failed, trying with baseline..."

  # Strategy 2: If database not clean, use baseline
  if atlas migrate apply --dir "file://migrations" --url "$DATABASE_URL" --baseline; then
    echo "✅ Atlas migrations completed successfully (baseline)"
  else
    echo "⚠️  Baseline migration failed, trying with allow-dirty..."

    # Strategy 3: If baseline fails, use allow-dirty
    if atlas migrate apply --dir "file://migrations" --url "$DATABASE_URL" --allow-dirty; then
      echo "✅ Atlas migrations completed successfully (allow-dirty)"
    else
      echo "❌ All migration strategies failed"
      exit 1
    fi
  fi
fi


# Wait for any process to exit
# wait -n

# Exit with status of process that exited first
exec $@
